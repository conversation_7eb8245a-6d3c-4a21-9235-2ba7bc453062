/**
 * 主播列表组件
 * 
 * 提供主播列表的展示、搜索、筛选和分页功能
 */

import { useState } from 'react'
import {Search, Filter, RefreshCw, Eye, Users, TrendingUp, Phone} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Badge } from '@/components/ui'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui'
import { Skeleton } from '@/components/ui'
import { ActionPermissionButton } from '@/components/auth/PermissionWrapper'
import { useAnchorList, useAnchorFilters } from '../hooks/useAnchorList'
import { OperationsUtils } from '@/services/operations'
import { useToast } from '@/hooks/useToast'
import { UserIdentity, UserState, AuthStatus } from '../types/operations'
import type { AnchorListResponse } from '../types/operations'
import {httpClient} from "@/services";

/**
 * 主播列表组件属性
 */
interface AnchorListProps {
  /** 选中主播回调 */
  onSelectAnchor?: (anchor: AnchorListResponse) => void
  /** 查看主播详情回调 */
  onViewDetails?: (anchor: AnchorListResponse) => void
}

/**
 * 主播列表组件
 */
export function AnchorList({ onSelectAnchor, onViewDetails }: AnchorListProps) {
  const [searchKeyword, setSearchKeyword] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [loadingPhones, setLoadingPhones] = useState<Set<number>>(new Set())
  const [fullPhones, setFullPhones] = useState<Map<number, string>>(new Map())

  const { toast } = useToast()
  const { filters, updateFilter, resetFilters } = useAnchorFilters()
  const {
    data,
    total,
    loading,
    error,
    currentPage,
    pageSize,
    refresh,
    search,
    changePage,
    changePageSize
  } = useAnchorList()
  
  /**
   * 处理搜索
   */
  const handleSearch = () => {
    const searchFilters = {
      ...filters,
      nickname: searchKeyword || undefined
    }
    search(searchFilters)
  }
  
  /**
   * 处理重置
   */
  const handleReset = () => {
    setSearchKeyword('')
    resetFilters()
  }
  
  /**
   * 处理主播选择
   */
  const handleSelectAnchor = (anchor: AnchorListResponse) => {
    onSelectAnchor?.(anchor)
  }

  /**
   * 处理获取完整手机号
   */
  const handleGetFullPhone = async (anchor: AnchorListResponse) => {
    if (!anchor.id || loadingPhones.has(anchor.id)) {
      return
    }

    // 如果已经获取过，直接显示
    if (fullPhones.has(anchor.id)) {
      const fullPhone = fullPhones.get(anchor.id)
      toast({
        title: '完整手机号',
        description: fullPhone,
        duration: 3000,
      })
      return
    }

    try {
      setLoadingPhones(prev => new Set(prev).add(anchor.id))

      const fullPhone = await httpClient.get<string>(`/operations/users/${anchor.id}/phone`)

      // 缓存结果
      setFullPhones(prev => new Map(prev).set(anchor.id, fullPhone))

      // 显示完整手机号
      toast({
        title: '完整手机号',
        description: fullPhone,
        duration: 3000,
      })

    } catch (error) {
      console.error('获取完整手机号失败:', error)
      toast({
        title: '获取失败',
        description: error instanceof Error ? error.message : '获取完整手机号失败',
        variant: 'destructive',
        duration: 3000,
      })
    } finally {
      setLoadingPhones(prev => {
        const newSet = new Set(prev)
        newSet.delete(anchor.id)
        return newSet
      })
    }
  }
  
  /**
   * 处理查看详情
   */
  const handleViewDetails = (anchor: AnchorListResponse) => {
    onViewDetails?.(anchor)
  }
  
  /**
   * 渲染筛选器
   */
  const renderFilters = () => {
    if (!showFilters) return null
    
    return (
      <Card className="mb-4">
        <CardHeader>
          <CardTitle className="text-sm">筛选条件</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">主播状态</label>
              <Select
                value={filters.status?.toString() || ''}
                onValueChange={(value) => updateFilter('status', value ? Number(value) as UserState : undefined)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部</SelectItem>
                  <SelectItem value={UserState.NORMAL.toString()}>正常</SelectItem>
                  <SelectItem value={UserState.DISABLED.toString()}>禁用</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">身份类型</label>
              <Select
                value={filters.identity?.toString() || ''}
                onValueChange={(value) => updateFilter('identity', value ? Number(value) as UserIdentity : undefined)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择身份" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部</SelectItem>
                  <SelectItem value={UserIdentity.ONLINE_ANCHOR.toString()}>线上主播</SelectItem>
                  <SelectItem value={UserIdentity.OFFLINE_ANCHOR.toString()}>线下主播</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">实名状态</label>
              <Select
                value={filters.isAuth?.toString() || ''}
                onValueChange={(value) => updateFilter('isAuth', value ? Number(value) as AuthStatus : undefined)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择认证状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部</SelectItem>
                  <SelectItem value={AuthStatus.AUTHED.toString()}>已实名</SelectItem>
                  <SelectItem value={AuthStatus.NOT_AUTH.toString()}>未实名</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div>
            <label className="text-sm font-medium mb-2 block">手机号</label>
            <Input
              placeholder="输入手机号"
              value={filters.phone || ''}
              onChange={(e) => updateFilter('phone', e.target.value || undefined)}
            />
          </div>
        </CardContent>
      </Card>
    )
  }
  
  /**
   * 渲染主播卡片
   */
  const renderAnchorCard = (anchor: AnchorListResponse) => {
    return (
      <Card key={anchor.id} className="hover:shadow-md transition-shadow cursor-pointer">
        <CardContent className="p-4">
          <div className="flex items-start space-x-4">
            <Avatar className="h-12 w-12">
              <AvatarImage src={anchor.userimage} alt={anchor.nickname} />
              <AvatarFallback>{anchor.nickname.charAt(0)}</AvatarFallback>
            </Avatar>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-sm truncate">{anchor.nickname}</h3>
                <div className="flex space-x-1">
                  <Badge 
                    variant={anchor.state === UserState.NORMAL ? 'default' : 'destructive'}
                    className="text-xs"
                  >
                    {OperationsUtils.getUserStateText(anchor.state)}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {OperationsUtils.getUserIdentityText(anchor.identity)}
                  </Badge>
                </div>
              </div>
              
              <div className="text-xs text-muted-foreground space-y-1">
                <div>用户名: {anchor.username}</div>
                <div className="flex items-center space-x-2">
                  <span>手机号:</span>
                  {anchor.phone && anchor.phone !== '-' ? (
                    <button
                      onClick={() => handleGetFullPhone(anchor)}
                      disabled={loadingPhones.has(anchor.id)}
                      className="inline-flex items-center space-x-1 text-blue-600 hover:text-blue-800 hover:underline transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      title="点击查看完整手机号"
                    >
                      <Phone className="h-3 w-3" />
                      <span>{anchor.phone}</span>
                      {loadingPhones.has(anchor.id) && (
                        <div className="animate-spin h-3 w-3 border border-blue-600 border-t-transparent rounded-full" />
                      )}
                    </button>
                  ) : (
                    <span>-</span>
                  )}
                </div>
                <div>邀请码: {anchor.inviteCode}</div>
                <div>
                  实名: {OperationsUtils.getAuthStatusText(anchor.isauth)}
                  <span className="ml-4">等级: Lv.{anchor.level}</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between mt-3 text-xs">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-1">
                    <Users className="h-3 w-3" />
                    <span>{anchor.subUserCount}人</span>
                  </div>
                  <div>余额: {OperationsUtils.formatAmount(anchor.coin)}元</div>
                  <div>钥匙: {OperationsUtils.formatAmount(anchor.key)}</div>
                </div>
                
                <div className="flex space-x-1">
                  {/* 统计按钮 - 需要统计查看权限 */}
                  <ActionPermissionButton
                    module="operations"
                    action="stats"
                    config={{
                      text: '统计',
                      icon: TrendingUp,
                      size: 'sm',
                      variant: 'outline',
                      onClick: () => handleSelectAnchor(anchor),
                      className: 'text-green-700 hover:text-green-800'
                    }}
                  />

                  {/* 详情按钮 - 需要详情查看权限 */}
                  <ActionPermissionButton
                    module="operations"
                    action="detail"
                    config={{
                      text: '详情',
                      icon: Eye,
                      size: 'sm',
                      variant: 'outline',
                      onClick: () => handleViewDetails(anchor),
                      className: 'text-blue-700 hover:text-blue-800'
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }
  
  /**
   * 渲染加载骨架
   */
  const renderSkeleton = () => {
    return Array.from({ length: 6 }).map((_, index) => (
      <Card key={index}>
        <CardContent className="p-4">
          <div className="flex items-start space-x-4">
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="flex-1 space-y-2">
              <Skeleton className="h-4 w-1/3" />
              <Skeleton className="h-3 w-1/2" />
              <Skeleton className="h-3 w-2/3" />
              <div className="flex justify-between">
                <Skeleton className="h-3 w-1/4" />
                <Skeleton className="h-6 w-16" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    ))
  }
  
  return (
    <div className="space-y-4">
      {/* 搜索和操作栏 */}
      <div className="flex items-center space-x-2">
        <div className="flex-1 flex items-center space-x-2">
          <Input
            placeholder="搜索主播昵称..."
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
            onKeyUp={(e) => e.key === 'Enter' && handleSearch()}
            className="max-w-sm"
          />
          <Button onClick={handleSearch} disabled={loading}>
            <Search className="h-4 w-4 mr-2" />
            搜索
          </Button>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-4 w-4 mr-2" />
            筛选
          </Button>
          <Button variant="outline" onClick={handleReset}>
            重置
          </Button>
          <Button variant="outline" onClick={refresh} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        </div>
      </div>
      
      {/* 筛选器 */}
      {renderFilters()}
      
      {/* 统计信息 */}
      <div className="text-sm text-muted-foreground">
        共找到 {total} 个主播
      </div>
      
      {/* 主播列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {loading ? renderSkeleton() : data.map(renderAnchorCard)}
      </div>
      
      {/* 错误提示 */}
      {error && (
        <div className="text-center py-8 text-red-500">
          {error}
        </div>
      )}
      
      {/* 空状态 */}
      {!loading && !error && data.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          暂无主播数据
        </div>
      )}
      
      {/* 分页 */}
      {total > 0 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            显示 {(currentPage - 1) * pageSize + 1} - {Math.min(currentPage * pageSize, total)} 条，共 {total} 条
          </div>
          
          <div className="flex items-center space-x-2">
            <Select
              value={pageSize.toString()}
              onValueChange={(value) => changePageSize(Number(value))}
            >
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
              </SelectContent>
            </Select>
            
            <div className="flex items-center space-x-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => changePage(currentPage - 1)}
                disabled={currentPage <= 1 || loading}
              >
                上一页
              </Button>
              <span className="text-sm px-2">
                {currentPage} / {Math.ceil(total / pageSize)}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => changePage(currentPage + 1)}
                disabled={currentPage >= Math.ceil(total / pageSize) || loading}
              >
                下一页
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
